## 📋 **Task: Add Dynamic Transaction Fields to "Other" Tab**

### **Objective**
Add the same dynamic transaction fields functionality (that was implemented in the bulk update form) to another page/popup, specifically in the "Other" tab positioned below the "Order Type" form.

### **Requirements**

#### **1. Location & Integration**
- **Target**: Add to the "Other" tab in [specify the exact page/popup name]
- **Position**: Below the existing "Order Type" form section
- **Integration**: Should seamlessly blend with existing UI/styling

#### **2. Functionality to Replicate**
Copy the exact same dynamic fields functionality from `application/modules/outlet_list/views/v2/form_bulkupdate_v2.php`:

**Core Features:**
- ✅ **Add Field Button** - Green "+ Add Field" button
- ✅ **Dynamic Field Creation** - Create unlimited custom fields
- ✅ **Field Types Support**:
  - Text input fields
  - Single Choice (radio buttons)
  - Multiple Choice (checkboxes)
- ✅ **Field Configuration**:
  - Custom field labels
  - Required/optional toggle
  - Dynamic options for choice fields
- ✅ **Field Management**:
  - Remove individual fields
  - Add/remove options for choice fields
  - Form reset integration

#### **3. Technical Implementation**

**Files to Reference:**
- Source: `application/modules/outlet_list/views/v2/form_bulkupdate_v2.php`
- Copy the Transaction Fields section (lines ~160-174)
- Copy the JavaScript functions (lines ~270-410)
- Copy the CSS styling (lines ~6-70)

**Form Data Structure:**
```html
transaction_fields[field_id][label]
transaction_fields[field_id][type] 
transaction_fields[field_id][is_required]
transaction_fields[field_id][options][]
```

#### **4. UI/UX Requirements**

**Styling:**
- Match the existing dark theme of the target page
- Use consistent spacing and alignment
- Follow the improved UI standards from the bulk update implementation
- Ensure responsive design (Bootstrap grid system)

**User Experience:**
- Smooth field addition/removal
- Intuitive option management for choice fields
- Clear visual hierarchy
- Proper form validation indicators

#### **5. Integration Points**

**Form Integration:**
- Integrate with existing form submission logic
- Add to form reset functionality
- Ensure proper form validation
- Handle form data serialization

**Database Preparation:**
- Fields should be structured to match `setting_transaction` table format
- Prepare for future backend integration (no database connection needed initially)

#### **6. Testing Requirements**

**Functional Testing:**
- ✅ Add multiple fields of different types
- ✅ Switch between field types and verify options show/hide
- ✅ Add/remove options for choice fields
- ✅ Mark fields as required/optional
- ✅ Remove individual fields
- ✅ Form reset clears all transaction fields
- ✅ Tab switching works properly (if applicable)

**UI Testing:**
- ✅ Responsive design on different screen sizes
- ✅ Proper spacing and alignment
- ✅ Button visibility and contrast
- ✅ Consistent styling with existing page

#### **7. Deliverables**

1. **Updated target file** with transaction fields section added
2. **JavaScript functions** for dynamic field management
3. **CSS styling** integrated with existing styles
4. **Test file** (optional) to demonstrate functionality
5. **Documentation** of form data structure for future backend integration

#### **8. Reference Implementation**

The developer can reference the working implementation in:
- `application/modules/outlet_list/views/v2/form_bulkupdate_v2.php` (Transaction Fields tab)
- `test_transaction_fields.html` (standalone demo)

**Key Functions to Copy:**
- `addTransactionField()`
- `removeTransactionField()`
- `updateFieldLabel()`
- `handleFieldTypeChange()`
- `addOption()` / `removeOption()`
- `resetTransactionFields()`

---

### **📝 Additional Notes**

- **No Backend Connection**: This is UI-only implementation initially
- **Future-Ready**: Structure data for easy backend integration later
- **Consistent Experience**: Should feel identical to the bulk update form
- **Maintainable Code**: Use the same patterns and naming conventions

**Estimated Time**: 2-4 hours (depending on target page complexity)

This task description gives your developer everything they need to replicate the transaction fields functionality in the new location while maintaining consistency and quality.
