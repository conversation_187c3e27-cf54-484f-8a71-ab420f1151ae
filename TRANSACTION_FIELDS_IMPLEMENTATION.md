# Transaction Fields Implementation - Outlet Form

## Overview
Successfully implemented dynamic transaction fields functionality in the outlet form's "Other" tab, replicating the exact same functionality from the bulk update form.

## Files Modified
- `application/modules/outlet_list/views/v2/outlet_v2.php` - Main outlet form file

## Implementation Details

### 1. CSS Styling Added
- Copied all transaction fields CSS from `form_bulkupdate_v2.php`
- Adapted selectors for the outlet form context (`#tab4` instead of `#tab-bulkupdate-transaction`)
- Maintained consistent dark theme styling
- Added responsive design support

### 2. HTML Structure Added
Located in the "Other" tab (`#tab4`), positioned below the "Order Type" form:
```html
<div class="form-group">
    <label>Transaction Fields:</label>
    <div class="small text-italic"><i>*Custom fields for transaction data collection.</i></div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <button type="button" class="btn btn-success btn-sm" onclick="addTransactionField()">
                    <i class="fa fa-plus"></i> Add Field
                </button>
            </div>
            <div id="transaction-fields-container">
                <!-- Transaction fields will be added here dynamically -->
            </div>
        </div>
    </div>
</div>
```

### 3. JavaScript Functions Added
All core functions from the bulk update form were copied and adapted:

#### Core Functions:
- `addTransactionField()` - Creates new dynamic fields
- `removeTransactionField(fieldId)` - Removes individual fields
- `updateFieldLabel(fieldId, label)` - Updates field display labels
- `handleFieldTypeChange(fieldId, type)` - Shows/hides options based on field type
- `addOption(fieldId)` - Adds options for choice fields
- `removeOption(optionId)` - Removes individual options
- `resetTransactionFields()` - Clears all transaction fields

#### Field Types Supported:
- **TEXT** - Simple text input fields
- **SINGLE_CHOICE** - Radio button options
- **MULTI_CHOICE** - Checkbox options

### 4. Form Integration

#### Form Reset Integration:
Added `resetTransactionFields()` calls to all form reset locations:
- `btnAdd()` function (when creating new outlet)
- `btnEdit()` function (when loading existing outlet)
- Form submission success handler (after successful save)

#### Form Submission Integration:
Added transaction fields data collection in the form submission handler:
```javascript
//get transaction fields
let transaction_fields_data = {};
$('#transaction-fields-container .transaction-field').each(function() {
    let fieldId = $(this).attr('id');
    let fieldLabel = $(this).find('input[name*="[label]"]').val();
    let fieldType = $(this).find('select[name*="[type]"]').val();
    let fieldRequired = $(this).find('input[name*="[is_required]"]').is(':checked') ? 1 : 0;
    let fieldOptions = [];

    // Get options for choice fields
    if (fieldType === 'SINGLE_CHOICE' || fieldType === 'MULTI_CHOICE') {
        $(this).find('input[name*="[options][]"]').each(function() {
            let optionValue = $(this).val();
            if (optionValue.trim() !== '') {
                fieldOptions.push(optionValue);
            }
        });
    }

    // Only add field if it has a label and type
    if (fieldLabel && fieldType) {
        transaction_fields_data[fieldId] = {
            label: fieldLabel,
            type: fieldType,
            is_required: fieldRequired,
            options: fieldOptions
        };
    }
});
formData.append('transaction_fields', JSON.stringify(transaction_fields_data));
```

#### Edit Data Loading:
Added transaction fields loading when editing existing outlets, handling the `setting_transaction` array from API:
```javascript
//transaction fields - handle setting_transaction array from API
if (data.setting_transaction && data.setting_transaction.length > 0) {
    data.setting_transaction.forEach(function(fieldData, index) {
        // Add the field
        addTransactionField();

        // Get the last added field (which should be the one we just added)
        let lastField = $('#transaction-fields-container .transaction-field').last();
        let actualFieldId = lastField.attr('id');

        // Update the field with the loaded data
        lastField.find('input[name*="[label]"]').val(fieldData.label).trigger('change');
        lastField.find('select[name*="[type]"]').val(fieldData.type).trigger('change');
        lastField.find('input[name*="[is_required]"]').prop('checked', fieldData.is_required === true || fieldData.is_required === 1);

        // Add options if it's a choice field and has option_values
        if ((fieldData.type === 'SINGLE_CHOICE' || fieldData.type === 'MULTI_CHOICE') && fieldData.option_values && fieldData.option_values.length > 0) {
            // Clear default options first
            $('#options_list_' + actualFieldId).empty();

            // Add each option from option_values array
            fieldData.option_values.forEach(function(optionText) {
                addOption(actualFieldId);
                let lastOption = $('#options_list_' + actualFieldId + ' .option-item').last();
                lastOption.find('input[type="text"]').val(optionText);
            });
        }

        // Store the original ID for potential updates (if needed for backend)
        lastField.attr('data-original-id', fieldData.id);
    });
}
```

### 5. Form Data Structure
The transaction fields data is structured to match the `setting_transaction` table format:
```javascript
transaction_fields: {
    "field_1": {
        "label": "Customer Name",
        "type": "TEXT",
        "is_required": 1,
        "options": []
    },
    "field_2": {
        "label": "Delivery Method",
        "type": "SINGLE_CHOICE",
        "is_required": 0,
        "options": ["Pickup", "Delivery", "Dine In"]
    }
}
```

## API Integration
The implementation now handles the actual API response structure with `setting_transaction` array:

### API Response Structure:
```json
{
  "status": "Success",
  "data": {
    "id": "29",
    "name": "Best UNIQ Resto Jojga",
    "setting_transaction": [
      {
        "id": 1,
        "outlet_id": 29,
        "label": "Status Member",
        "type": "SINGLE_CHOICE",
        "option_values": ["Member Baru", "Member Lama"],
        "values_config": null,
        "is_required": true,
        "display_order": 0,
        "status": "on",
        "created_at": "1749788269530",
        "updated_at": "1749788269530"
      },
      {
        "id": 2,
        "outlet_id": 29,
        "label": "No. HP",
        "type": "TEXT",
        "option_values": null,
        "values_config": {"print_receipt": true},
        "is_required": true,
        "display_order": 0,
        "status": "on",
        "created_at": "1749788269530",
        "updated_at": "1749788269530"
      }
    ]
  }
}
```

### Key Mapping:
- `setting_transaction[].label` → Field Label
- `setting_transaction[].type` → Field Type (TEXT, SINGLE_CHOICE, MULTI_CHOICE)
- `setting_transaction[].is_required` → Required checkbox
- `setting_transaction[].option_values[]` → Options for choice fields
- `setting_transaction[].id` → Stored as `data-original-id` for updates

## Testing
Created `test_transaction_fields_outlet.html` - a standalone test file that demonstrates all functionality:
- Adding/removing fields
- Changing field types
- Managing options for choice fields
- Form data serialization
- Reset functionality
- **NEW**: Loading sample API data with "Load Sample API Data" button

## Features Implemented ✅
- ✅ Add Field Button - Green "+ Add Field" button
- ✅ Dynamic Field Creation - Create unlimited custom fields
- ✅ Field Types Support - Text, Single Choice, Multiple Choice
- ✅ Field Configuration - Custom labels, required/optional toggle, dynamic options
- ✅ Field Management - Remove individual fields, add/remove options
- ✅ Form Reset Integration - Clears transaction fields on form reset
- ✅ Form Submission Integration - Serializes transaction fields data
- ✅ Edit Data Loading - Loads existing transaction fields when editing
- ✅ Consistent UI/UX - Matches existing dark theme and styling
- ✅ Responsive Design - Works on different screen sizes

## Backend Integration Ready
The implementation is structured to be ready for backend integration:
- Data is serialized as JSON and sent via `transaction_fields` form parameter
- Structure matches expected database format
- No database connection required initially (UI-only implementation)

## Next Steps
1. Backend API should handle the `transaction_fields` parameter
2. Store/retrieve transaction fields data in the `setting_transaction` table
3. Validate field data on the backend
4. Consider adding more field types if needed (date, number, etc.)

## Maintenance Notes
- Uses the same patterns and naming conventions as the bulk update form
- All functions are properly namespaced to avoid conflicts
- Code is well-commented and follows existing code style
- Easy to extend with additional field types or features
