<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Fields Test - Outlet Form</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            background: #2c2e2f;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        .container {
            margin-top: 20px;
        }
        .panel {
            background: #3a3c3d;
            border-color: #555;
        }
        .panel-heading {
            background: #4a4c4d;
            border-color: #555;
        }
        .form-control {
            background: #2c2e2f;
            border-color: #555;
            color: #fff;
        }
        .form-control:focus {
            background: #2c2e2f;
            border-color: #6aa50d;
            color: #fff;
        }
        
        /* Transaction Fields Styling */
        .transaction-field {
            margin-bottom: 15px;
        }
        .transaction-field .panel-heading {
            padding: 8px 15px;
        }
        .transaction-field .panel-title {
            font-size: 14px;
            margin: 0;
            line-height: 1.4;
        }
        .transaction-field .panel-body {
            padding: 18px;
        }
        .options-container {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #555;
        }
        .option-item {
            margin-bottom: 8px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .option-item .form-control {
            flex: 1;
        }
        .field-label {
            font-weight: bold;
        }
        .btn-add-option {
            border: 1px solid rgba(255,255,255,0.08);
            background: rgba(255,255,255,0.03);
        }
        .option-item .btn-danger {
            margin-left: 6px;
        }
        .transaction-field .panel-title .btn-danger {
            margin-right: 12px;
        }
        .checkbox label {
            line-height: 36px;
            vertical-align: middle;
        }
        .checkbox input[type="checkbox"] {
            vertical-align: middle;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h2>Transaction Fields Test - Outlet Form</h2>
                <p class="text-muted">This is a standalone test of the transaction fields functionality added to the outlet form.</p>
                
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>Transaction Fields Section</h4>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label>Transaction Fields:</label>
                            <div class="small text-italic"><i>*Custom fields for transaction data collection.</i></div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button type="button" class="btn btn-success btn-sm" onclick="addTransactionField()">
                                            <i class="fa fa-plus"></i> Add Field
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="resetTransactionFields()" style="margin-left: 10px;">
                                            <i class="fa fa-refresh"></i> Reset All
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" onclick="showFormData()" style="margin-left: 10px;">
                                            <i class="fa fa-eye"></i> Show Form Data
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="loadSampleData()" style="margin-left: 10px;">
                                            <i class="fa fa-download"></i> Load Sample API Data
                                        </button>
                                    </div>
                                    <div id="transaction-fields-container">
                                        <!-- Transaction fields will be added here dynamically -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>Form Data Output</h4>
                    </div>
                    <div class="panel-body">
                        <pre id="form-data-output" style="background: #1e1e1e; color: #fff; padding: 15px; border-radius: 4px;">
Click "Show Form Data" to see the current form data structure.
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
    <script>
        /* TRANSACTION FIELDS FUNCTIONS */
        let transactionFieldCounter = 0;

        function addTransactionField() {
            transactionFieldCounter++;
            let fieldId = 'field_' + transactionFieldCounter;

            let fieldHtml = `
                <div class="panel panel-default transaction-field" id="${fieldId}" style="background: #3a3c3d; border-color: #555;">
                    <div class="panel-heading" style="background: #4a4c4d; border-color: #555;">
                        <h4 class="panel-title" style="color: #fff;">
                            <span class="field-label">New Field</span>
                            <button type="button" class="btn btn-danger btn-xs pull-right" onclick="removeTransactionField('${fieldId}')">
                                <i class="fa fa-trash"></i> Remove
                            </button>
                            <div class="clearfix"></div>
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Field Label*</label>
                                    <input type="text" class="form-control field-label-input" name="transaction_fields[${fieldId}][label]"
                                           placeholder="Enter field label" onchange="updateFieldLabel('${fieldId}', this.value)">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Field Type*</label>
                                    <select class="form-control field-type-select" name="transaction_fields[${fieldId}][type]"
                                            onchange="handleFieldTypeChange('${fieldId}', this.value)">
                                        <option value="">Select Type</option>
                                        <option value="TEXT">Text</option>
                                        <option value="SINGLE_CHOICE">Single Choice</option>
                                        <option value="MULTI_CHOICE">Multiple Choice</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Required</label>
                                    <div class="checkbox" style="margin-top: 7px;">
                                        <label style="color: #fff;">
                                            <input type="checkbox" name="transaction_fields[${fieldId}][is_required]" value="1">
                                            Required Field
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row options-container" id="options_${fieldId}" style="display: none;">
                            <div class="col-md-12">
                                <label>Options*</label>
                                <div class="options-list" id="options_list_${fieldId}">
                                    <!-- Options will be added here -->
                                </div>
                                <button type="button" class="btn btn-info btn-sm btn-add-option" onclick="addOption('${fieldId}')">
                                    <i class="fa fa-plus"></i> Add Option
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#transaction-fields-container').append(fieldHtml);
        }

        function removeTransactionField(fieldId) {
            $('#' + fieldId).remove();
        }

        function updateFieldLabel(fieldId, label) {
            $('#' + fieldId + ' .field-label').text(label || 'New Field');
        }

        function handleFieldTypeChange(fieldId, type) {
            let optionsContainer = $('#options_' + fieldId);

            if (type === 'SINGLE_CHOICE' || type === 'MULTI_CHOICE') {
                optionsContainer.show();
                // Add default options if none exist
                if ($('#options_list_' + fieldId + ' .option-item').length === 0) {
                    addOption(fieldId);
                    addOption(fieldId);
                }
            } else {
                optionsContainer.hide();
                $('#options_list_' + fieldId).empty();
            }
        }

        let optionCounter = 0;

        function addOption(fieldId) {
            optionCounter++;
            let optionId = 'option_' + fieldId + '_' + optionCounter;

            let optionHtml = `
                <div class="option-item" id="${optionId}">
                    <input type="text" class="form-control" name="transaction_fields[${fieldId}][options][]"
                           placeholder="Enter option text">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeOption('${optionId}')">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            `;

            $('#options_list_' + fieldId).append(optionHtml);
        }

        function removeOption(optionId) {
            $('#' + optionId).remove();
        }

        function resetTransactionFields() {
            $('#transaction-fields-container').empty();
            transactionFieldCounter = 0;
            optionCounter = 0;
            $('#form-data-output').text('Transaction fields have been reset.');
        }

        function showFormData() {
            let transaction_fields_data = {};
            $('#transaction-fields-container .transaction-field').each(function() {
                let fieldId = $(this).attr('id');
                let fieldLabel = $(this).find('input[name*="[label]"]').val();
                let fieldType = $(this).find('select[name*="[type]"]').val();
                let fieldRequired = $(this).find('input[name*="[is_required]"]').is(':checked') ? 1 : 0;
                let fieldOptions = [];

                // Get options for choice fields
                if (fieldType === 'SINGLE_CHOICE' || fieldType === 'MULTI_CHOICE') {
                    $(this).find('input[name*="[options][]"]').each(function() {
                        let optionValue = $(this).val();
                        if (optionValue.trim() !== '') {
                            fieldOptions.push(optionValue);
                        }
                    });
                }

                // Only add field if it has a label and type
                if (fieldLabel && fieldType) {
                    transaction_fields_data[fieldId] = {
                        label: fieldLabel,
                        type: fieldType,
                        is_required: fieldRequired,
                        options: fieldOptions
                    };
                }
            });

            $('#form-data-output').text(JSON.stringify(transaction_fields_data, null, 2));
        }

        function loadSampleData() {
            // Sample API response data (like the one you provided)
            let sampleApiData = {
                "setting_transaction": [
                    {
                        "id": 1,
                        "outlet_id": 29,
                        "label": "Status Member",
                        "type": "SINGLE_CHOICE",
                        "option_values": ["Member Baru", "Member Lama"],
                        "values_config": null,
                        "is_required": true,
                        "display_order": 0,
                        "status": "on",
                        "created_at": "1749788269530",
                        "updated_at": "1749788269530"
                    },
                    {
                        "id": 2,
                        "outlet_id": 29,
                        "label": "No. HP",
                        "type": "TEXT",
                        "option_values": null,
                        "values_config": {"print_receipt": true},
                        "is_required": true,
                        "display_order": 0,
                        "status": "on",
                        "created_at": "1749788269530",
                        "updated_at": "1749788269530"
                    }
                ]
            };

            // Clear existing fields first
            resetTransactionFields();

            // Load the sample data (simulating the API response loading)
            if (sampleApiData.setting_transaction && sampleApiData.setting_transaction.length > 0) {
                sampleApiData.setting_transaction.forEach(function(fieldData, index) {
                    // Add the field
                    addTransactionField();

                    // Get the last added field (which should be the one we just added)
                    let lastField = $('#transaction-fields-container .transaction-field').last();
                    let actualFieldId = lastField.attr('id');

                    // Update the field with the loaded data
                    lastField.find('input[name*="[label]"]').val(fieldData.label).trigger('change');
                    lastField.find('select[name*="[type]"]').val(fieldData.type).trigger('change');
                    lastField.find('input[name*="[is_required]"]').prop('checked', fieldData.is_required === true || fieldData.is_required === 1);

                    // Add options if it's a choice field and has option_values
                    if ((fieldData.type === 'SINGLE_CHOICE' || fieldData.type === 'MULTI_CHOICE') && fieldData.option_values && fieldData.option_values.length > 0) {
                        // Clear default options first
                        $('#options_list_' + actualFieldId).empty();

                        // Add each option from option_values array
                        fieldData.option_values.forEach(function(optionText) {
                            addOption(actualFieldId);
                            let lastOption = $('#options_list_' + actualFieldId + ' .option-item').last();
                            lastOption.find('input[type="text"]').val(optionText);
                        });
                    }

                    // Store the original ID for potential updates (if needed for backend)
                    lastField.attr('data-original-id', fieldData.id);
                });
            }

            $('#form-data-output').text('Sample API data loaded successfully! Click "Show Form Data" to see the current form structure.');
        }
    </script>
</body>
</html>
